"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/view/page",{

/***/ "(app-pages-browser)/./src/app/ui/inventory/inventory.tsx":
/*!********************************************!*\
  !*** ./src/app/ui/inventory/inventory.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Inventory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_ui_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/ui/editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _maintenance_list_list__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ../maintenance/list/list */ \"(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\");\n/* harmony import */ var _barrel_optimize_names_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _logbook_components_CloudFlareCaptures__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ../logbook/components/CloudFlareCaptures */ \"(app-pages-browser)/./src/app/ui/logbook/components/CloudFlareCaptures.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Inventory(param) {\n    let { inventoryID, inventoryTab = \"\" } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"info\");\n    const [inventory, setInventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedCategories, setSelectedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [suppliers, setSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedSuppliers, setSelectedSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    //    const [attachments, setAttachments] = useState<any>()\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [taskCounter, setTaskCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [displayTask, setDisplayTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [openLocationDialog, setOpenLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openSupplierDialog, setOpenSupplierDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openCategoryDialog, setOpenCategoryDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openConfirmInventoryDeleteDialog, setOpenConfirmInventoryDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fileLinks, setFileLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [linkSelectedOption, setLinkSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_inventory, setEdit_inventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [delete_inventory, setDelete_inventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [view_inventory, setView_inventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_22__.useVesselIconData)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_23__.useBreakpoints)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_INVENTORY\", permissions)) {\n                setEdit_inventory(true);\n            } else {\n                setEdit_inventory(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"DELETE_INVENTORY\", permissions)) {\n                setDelete_inventory(true);\n            } else {\n                setDelete_inventory(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_INVENTORY\", permissions)) {\n                setView_inventory(true);\n            } else {\n                setView_inventory(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const handleSetInventory = (data)=>{\n        var _data_categories_nodes, _data_categories, _data_suppliers_nodes, _data_suppliers, _data_documents, _data_attachmentLinks;\n        const getContent = ()=>{\n            var _data_content;\n            const content = data.content !== \"null\" ? (_data_content = data.content) !== null && _data_content !== void 0 ? _data_content : \"\" : \"\";\n            var _data_description;\n            const description = data.description !== \"null\" ? (_data_description = data.description) !== null && _data_description !== void 0 ? _data_description : \"\" : \"\";\n            return \"\".concat(content, \" \").concat(description).trim();\n        };\n        setInventory({\n            ...data,\n            content: getContent()\n        });\n        if (inventoryTab === \"maintenance\") {\n            setDisplayTask(true);\n        }\n        setSelectedLocation({\n            label: data === null || data === void 0 ? void 0 : data.location,\n            value: 0\n        });\n        setSelectedCategories(data === null || data === void 0 ? void 0 : (_data_categories = data.categories) === null || _data_categories === void 0 ? void 0 : (_data_categories_nodes = _data_categories.nodes) === null || _data_categories_nodes === void 0 ? void 0 : _data_categories_nodes.map((category)=>({\n                label: category.name,\n                value: category.id\n            })));\n        setSelectedSuppliers(data === null || data === void 0 ? void 0 : (_data_suppliers = data.suppliers) === null || _data_suppliers === void 0 ? void 0 : (_data_suppliers_nodes = _data_suppliers.nodes) === null || _data_suppliers_nodes === void 0 ? void 0 : _data_suppliers_nodes.map((supplier)=>({\n                label: supplier.name,\n                value: supplier.id\n            })));\n        queryMaintenanceCheck({\n            variables: {\n                inventoryID: +inventoryID,\n                vesselID: 0\n            }\n        });\n        setDocuments(data === null || data === void 0 ? void 0 : (_data_documents = data.documents) === null || _data_documents === void 0 ? void 0 : _data_documents.nodes);\n        setLinkSelectedOption(data === null || data === void 0 ? void 0 : (_data_attachmentLinks = data.attachmentLinks) === null || _data_attachmentLinks === void 0 ? void 0 : _data_attachmentLinks.nodes.map((link)=>({\n                label: link.link,\n                value: link.id\n            })));\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getInventoryByID)(inventoryID, handleSetInventory);\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const vesselList = activeVessels.map((item)=>({\n                ...item\n            }));\n        const appendedData = [\n            // { title: '-- Other --', id: 'newLocation' },\n            ...vesselList,\n            {\n                title: \"Other\",\n                id: \"0\"\n            }\n        ];\n        setVessels(appendedData);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getVesselList)(handleSetVessels);\n    const handelSetSuppliers = (data)=>{\n        const suppliersList = [\n            {\n                label: \" ---- Create supplier ---- \",\n                value: \"newSupplier\"\n            },\n            ...data === null || data === void 0 ? void 0 : data.filter((supplier)=>supplier.name !== null).map((supplier)=>({\n                    label: supplier.name,\n                    value: supplier.id\n                }))\n        ];\n        setSuppliers(suppliersList);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getSupplier)(handelSetSuppliers);\n    const handleSetCategories = (data)=>{\n        const formattedData = [\n            {\n                label: \" ---- Create Category ---- \",\n                value: \"newCategory\",\n                excludeFromSelectAll: true\n            },\n            ...data === null || data === void 0 ? void 0 : data.filter((category)=>category.name !== null && category.archived === false).map((category)=>({\n                    label: category.name,\n                    value: category.id\n                }))\n        ];\n        setCategories(formattedData);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getInventoryCategory)(handleSetCategories);\n    const [queryMaintenanceCheck] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_CHECK_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].list;\n            if (data) {\n                const activeTasks = data.filter((task)=>(task === null || task === void 0 ? void 0 : task.archived) != 1);\n                setTasks(data);\n                const taskCounter = activeTasks.filter((task)=>task.isOverDue.status === \"High\").length;\n                setTaskCounter(taskCounter);\n                const appendedData = Array.from(new Set(data.filter((item)=>item.assignedToID > 0).map((item)=>item.assignedToID)));\n                loadCrewMemberInfo(appendedData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceCheck error\", error);\n        }\n    });\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleSetSelectedCategories = (selectedOption)=>{\n        if (selectedOption.find((option)=>option.value === \"newCategory\")) {\n            setOpenCategoryDialog(true);\n        }\n        setSelectedCategories(selectedOption.filter((option)=>option.value !== \"newCategory\"));\n    };\n    const handleEditorChange = (content)=>{\n        setInventory({\n            ...inventory,\n            content: content\n        });\n    };\n    const handleSave = async ()=>{\n        var _inventory_attachmentLinks;\n        if (!inventory) {\n            console.error(\"Inventory page has not been initialised, possibly a slow internet connection, please try after a few seconds\");\n            sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(\"Please wait to initialize the inventory before saving\");\n            return;\n        }\n        if (!edit_inventory) {\n            sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(\"You do not have permission to edit this inventory\");\n            return;\n        }\n        const variables = {\n            input: {\n                id: +inventory.id,\n                item: document.getElementById(\"inventory-name\").value ? document.getElementById(\"inventory-name\").value : inventory.item,\n                title: document.getElementById(\"inventory-name\").value ? document.getElementById(\"inventory-name\").value : inventory.title,\n                location: document.getElementById(\"inventory-location\").value ? document.getElementById(\"inventory-location\").value : inventory.location,\n                description: null,\n                content: inventory.content,\n                quantity: document.getElementById(\"inventory-qty\").value ? parseInt(document.getElementById(\"inventory-qty\").value) : inventory.quantity,\n                productCode: document.getElementById(\"inventory-code\").value ? document.getElementById(\"inventory-code\").value : inventory.productCode,\n                costingDetails: document.getElementById(\"inventory-cost\").value ? document.getElementById(\"inventory-cost\").value : inventory.costingDetails,\n                comments: document.getElementById(\"inventory-comments\").value ? document.getElementById(\"inventory-comments\").value : inventory.comments,\n                archived: inventory.archived,\n                inventoryImportID: inventory.inventoryImportID,\n                vesselID: selectedLocation.value ? selectedLocation.value : inventory.vesselID,\n                // attachments: inventory.attachments,\n                documents: documents.map((doc)=>doc.id).join(\",\"),\n                categories: (selectedCategories === null || selectedCategories === void 0 ? void 0 : selectedCategories.map((category)=>category.value).length) ? selectedCategories.map((category)=>category.value).join(\",\") : inventory.categories.nodes.map((categories)=>categories.id).join(\",\"),\n                suppliers: (selectedSuppliers === null || selectedSuppliers === void 0 ? void 0 : selectedSuppliers.map((supplier)=>supplier.value).length) ? selectedSuppliers.map((supplier)=>supplier.value).join(\",\") : inventory.suppliers.nodes.map((supplier)=>supplier.id).join(\",\"),\n                attachmentLinks: linkSelectedOption ? linkSelectedOption.map((link)=>link.value).join(\",\") : (_inventory_attachmentLinks = inventory.attachmentLinks) === null || _inventory_attachmentLinks === void 0 ? void 0 : _inventory_attachmentLinks.nodes.map((link)=>link.id).join(\",\")\n            }\n        };\n        await mutationUpdateInventory({\n            variables\n        });\n    };\n    const [mutationUpdateInventory, { loading: mutationupdateInventoryLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_5__.UPDATE_INVENTORY, {\n        onCompleted: (response)=>{\n            const data = response.updateInventory;\n            if (data.id > 0) {\n                searchParams.get(\"redirect_to\") ? router.push((searchParams === null || searchParams === void 0 ? void 0 : searchParams.get(\"redirect_to\")) + \"\") : router.back();\n            } else {\n                console.error(\"mutationupdateInventory error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationupdateInventory error\", error);\n        }\n    });\n    const handleCreateCategory = async ()=>{\n        const categoryName = document.getElementById(\"inventory-new-category\").value;\n        return await mutationcreateInventoryCategory({\n            variables: {\n                input: {\n                    name: categoryName\n                }\n            }\n        });\n    };\n    const [mutationcreateInventoryCategory, { loading: mutationcreateInventoryCategoryLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_5__.CREATE_INVENTORY_CATEGORY, {\n        onCompleted: (response)=>{\n            const data = response.createInventoryCategory;\n            if (data.id > 0) {\n                const formattedData = [\n                    ...categories,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setCategories(formattedData);\n                const categoriesList = [\n                    ...selectedCategories,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSelectedCategories(categoriesList);\n                setOpenCategoryDialog(false);\n            } else {\n                console.error(\"mutationcreateInventoryCategory error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateInventoryCategory error\", error);\n        }\n    });\n    const handleDeleteInventories = async ()=>{\n        if (!delete_inventory) {\n            sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(\"You do not have permission to delete this inventory\");\n            return;\n        }\n        await mutationDeleteInventories({\n            variables: {\n                ids: [\n                    +inventory.id\n                ]\n            }\n        });\n    };\n    const [mutationDeleteInventories, { loading: mutationdeleteInventoriesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_5__.DELETE_INVENTORIES, {\n        onCompleted: (response)=>{\n            if (response.deleteInventories && response.deleteInventories.length > 0) {\n                router.push(\"/inventory\");\n            } else {\n                console.error(\"mutationdeleteInventories failed to delete:\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationdeleteInventories error:\", error.message);\n        }\n    });\n    const handleSelectedVesselChange = (selectedOption)=>{\n        if (selectedOption && selectedOption.value === \"newLocation\") {\n            setOpenLocationDialog(true);\n        }\n        setSelectedLocation(selectedOption);\n    };\n    const handleCreateLocation = (Location)=>{\n        var newLocation = {\n            label: \"\",\n            value: \"\"\n        };\n        if (typeof Location === \"string\") {\n            newLocation = {\n                label: Location,\n                value: Location\n            };\n        }\n        if (typeof Location === \"object\") {\n            newLocation = {\n                label: document.getElementById(\"inventory-new-location\").value,\n                value: document.getElementById(\"inventory-new-location-id\").value ? document.getElementById(\"inventory-new-location-id\").value : document.getElementById(\"inventory-new-location\").value\n            };\n        }\n        const vesselList = vessels.map((item)=>({\n                ...item\n            }));\n        const appendedData = [\n            ...vesselList,\n            {\n                Title: newLocation.label,\n                ID: newLocation.value\n            }\n        ];\n        setVessels(appendedData);\n        setSelectedLocation(newLocation);\n        setOpenLocationDialog(false);\n    };\n    const deleteFile = async (id)=>{\n        const newDocuments = documents.filter((doc)=>doc.id !== id);\n        setDocuments(newDocuments);\n    };\n    const handleDisplayTask = ()=>{\n        setDisplayTask(true);\n    };\n    const handleSelectedSuppliers = (selectedOption)=>{\n        if (selectedOption.find((option)=>option.value === \"newSupplier\")) {\n            setOpenSupplierDialog(true);\n        }\n        setSelectedSuppliers(selectedOption.filter((option)=>option.value !== \"newSupplier\"));\n    };\n    const handleCreateSupplier = async ()=>{\n        const name = document.getElementById(\"supplier-name\").value;\n        const website = document.getElementById(\"supplier-website\").value;\n        const phone = document.getElementById(\"supplier-phone\").value;\n        const email = document.getElementById(\"supplier-email\").value;\n        const address = document.getElementById(\"supplier-address\").value;\n        const variables = {\n            input: {\n                name: name,\n                address: address,\n                website: website,\n                email: email,\n                phone: phone\n            }\n        };\n        if (name !== \"\") {\n            await mutationCreateSupplier({\n                variables\n            });\n        }\n        setOpenSupplierDialog(false);\n    };\n    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_5__.CREATE_SUPPLIER, {\n        onCompleted: (response)=>{\n            const data = response.createSupplier;\n            if (data.id > 0) {\n                const suppliersList = [\n                    ...suppliers,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSuppliers(suppliersList);\n                const selectedSuppliersList = [\n                    ...selectedSuppliers,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSelectedSuppliers(selectedSuppliersList);\n            } else {\n                console.error(\"mutationcreateSupplier error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplier error\", error);\n        }\n    });\n    const [createSeaLogsFileLinks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_5__.CREATE_SEALOGS_FILE_LINKS, {\n        onCompleted: (response)=>{\n            const data = response.createSeaLogsFileLinks;\n            if (data.id > 0) {\n                const newLinks = [\n                    ...fileLinks,\n                    data\n                ];\n                setFileLinks(newLinks);\n                linkSelectedOption ? setLinkSelectedOption([\n                    ...linkSelectedOption,\n                    {\n                        label: data.link,\n                        value: data.id\n                    }\n                ]) : setLinkSelectedOption([\n                    {\n                        label: data.link,\n                        value: data.id\n                    }\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createSeaLogsFileLinksEntry error\", error);\n        }\n    });\n    const handleDeleteLink = (link)=>{\n        setLinkSelectedOption(linkSelectedOption.filter((l)=>l !== link));\n    };\n    const linkItem = (link)=>{\n        if (!link.label) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between align-middle mr-2 w-fit\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    href: link.label,\n                    target: \"_blank\",\n                    className: \"ml-2 \",\n                    children: link.label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                    lineNumber: 684,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-2 \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"destructive\",\n                        iconOnly: true,\n                        iconLeft: _barrel_optimize_names_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n                        onClick: ()=>handleDeleteLink(link)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 688,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                    lineNumber: 687,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n            lineNumber: 683,\n            columnNumber: 13\n        }, this);\n    };\n    if (!permissions || !view_inventory) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n            lineNumber: 701,\n            columnNumber: 13\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n            lineNumber: 703,\n            columnNumber: 13\n        }, this);\n    }\n    const confirmInventoryCrew = ()=>{\n        if (inventory) {\n            setOpenConfirmInventoryDeleteDialog(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"text-2xl font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-muted-foreground mr-2\",\n                                    children: \"Inventory:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 25\n                                }, this),\n                                inventory === null || inventory === void 0 ? void 0 : inventory.item\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                            lineNumber: 717,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 716,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_16__.Separator, {\n                        className: \"mb-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.Tabs, {\n                            defaultValue: \"info\",\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsTrigger, {\n                                            value: \"info\",\n                                            children: \"Item Info\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsTrigger, {\n                                            value: \"tasks\",\n                                            className: \"relative\",\n                                            children: [\n                                                \"Tasks / Maintenance\",\n                                                taskCounter > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_17__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"ml-2 h-4 \".concat(taskCounter > 0 ? \"bg-rose-100 text-rose-700 hover:bg-rose-200\" : \"bg-emerald-100 text-emerald-700 hover:bg-emerald-200\"),\n                                                    children: taskCounter\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsContent, {\n                                    value: \"tasks\",\n                                    className: \"pt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full\",\n                                        children: tasks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_maintenance_list_list__WEBPACK_IMPORTED_MODULE_21__.MaintenanceTable, {\n                                            maintenanceChecks: tasks,\n                                            vessels: vessels,\n                                            crewInfo: crewInfo\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 37\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                        lineNumber: 745,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsContent, {\n                                    value: \"info\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6 py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid sm:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                id: \"inventory-name\",\n                                                                type: \"text\",\n                                                                defaultValue: inventory === null || inventory === void 0 ? void 0 : inventory.item,\n                                                                placeholder: \"Inventory name\",\n                                                                readOnly: !edit_inventory\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            vessels && inventory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_13__.Combobox, {\n                                                                id: \"inventory-vessel\",\n                                                                options: vessels === null || vessels === void 0 ? void 0 : vessels.map((vessel)=>{\n                                                                    const vesselWithIcon = getVesselWithIcon(vessel.id, vessel);\n                                                                    return {\n                                                                        label: vessel.title,\n                                                                        value: vessel.id,\n                                                                        vessel: vesselWithIcon\n                                                                    };\n                                                                }),\n                                                                isDisabled: !edit_inventory,\n                                                                defaultValues: (inventory === null || inventory === void 0 ? void 0 : inventory.vesselID) && (inventory === null || inventory === void 0 ? void 0 : inventory.vesselID) == 0 ? {\n                                                                    label: \"Other\",\n                                                                    value: \"0\"\n                                                                } : (inventory === null || inventory === void 0 ? void 0 : inventory.vessel) ? {\n                                                                    label: inventory.vessel.title,\n                                                                    value: inventory.vessel.id,\n                                                                    vessel: getVesselWithIcon(inventory.vessel.id, inventory.vessel)\n                                                                } : null,\n                                                                className: \"w-full\",\n                                                                placeholder: \"Select Vessel\",\n                                                                onChange: handleSelectedVesselChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 45\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_8__.InputSkeleton, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 818,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid sm:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                id: \"inventory-location\",\n                                                                type: \"text\",\n                                                                defaultValue: inventory === null || inventory === void 0 ? void 0 : inventory.location,\n                                                                placeholder: \"Location\",\n                                                                readOnly: !edit_inventory\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                id: \"inventory-qty\",\n                                                                type: \"number\",\n                                                                defaultValue: inventory === null || inventory === void 0 ? void 0 : inventory.quantity,\n                                                                placeholder: \"Quantity\",\n                                                                readOnly: !edit_inventory\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 831,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium mb-2\",\n                                                                children: \"Description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground text-sm leading-relaxed\",\n                                                                children: \"Enter details that might help with the maintenance or operation of this item.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 845,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 841,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-2 space-y-4\",\n                                                        children: inventory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_editor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            id: \"inventory-Content\",\n                                                            content: inventory === null || inventory === void 0 ? void 0 : inventory.content,\n                                                            handleEditorChange: handleEditorChange\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_16__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium mb-2\",\n                                                                children: \"Inventory details\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 869,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground text-sm leading-relaxed\",\n                                                                children: \"In this section categorise the item and add the suppliers where you normally purchase this item and the expected cost. This will help replacing the item in the future.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 872,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 868,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                            className: \"space-y-5\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                                                                    label: \"Product code\",\n                                                                    htmlFor: \"inventory-code\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                        id: \"inventory-code\",\n                                                                        type: \"text\",\n                                                                        defaultValue: inventory === null || inventory === void 0 ? void 0 : inventory.productCode,\n                                                                        placeholder: \"Product code\",\n                                                                        readOnly: !edit_inventory\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                        lineNumber: 885,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 882,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                inventory && categories ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_13__.Combobox, {\n                                                                    className: \"w-full\",\n                                                                    label: \"Categories\",\n                                                                    id: \"inventory-categories\",\n                                                                    defaultValues: inventory.categories && inventory.categories.nodes.map((category)=>({\n                                                                            label: category.name,\n                                                                            value: category.id\n                                                                        })),\n                                                                    isDisabled: !edit_inventory,\n                                                                    value: selectedCategories,\n                                                                    multi: true,\n                                                                    options: categories,\n                                                                    onChange: handleSetSelectedCategories\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 896,\n                                                                    columnNumber: 49\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_8__.InputSkeleton, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 920,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                inventory && suppliers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_13__.Combobox, {\n                                                                    label: \"Supplier\",\n                                                                    className: \"w-full\",\n                                                                    id: \"inventory-suppliers\",\n                                                                    defaultValues: inventory.Suppliers && (suppliers === null || suppliers === void 0 ? void 0 : suppliers.filter((supplier)=>{\n                                                                        var _supplier_value;\n                                                                        return (inventory === null || inventory === void 0 ? void 0 : inventory.Suppliers) && Object.keys(inventory.Suppliers).includes(supplier === null || supplier === void 0 ? void 0 : (_supplier_value = supplier.value) === null || _supplier_value === void 0 ? void 0 : _supplier_value.toString());\n                                                                    }).map((supplier)=>({\n                                                                            label: supplier.label,\n                                                                            value: supplier.value\n                                                                        }))),\n                                                                    multi: true,\n                                                                    isDisabled: !edit_inventory,\n                                                                    value: selectedSuppliers,\n                                                                    onChange: handleSelectedSuppliers,\n                                                                    options: suppliers\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 924,\n                                                                    columnNumber: 49\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_8__.InputSkeleton, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 960,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                                                                    label: \"Cost\",\n                                                                    htmlFor: \"inventory-cost\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                        id: \"inventory-cost\",\n                                                                        type: \"text\",\n                                                                        defaultValue: inventory === null || inventory === void 0 ? void 0 : inventory.costingDetails,\n                                                                        placeholder: \"Costing details\",\n                                                                        readOnly: !edit_inventory\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                        lineNumber: 966,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 963,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                                                                            label: \"Links\",\n                                                                            htmlFor: \"task-title\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                id: \"task-title\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Type a link and press Enter\",\n                                                                                readOnly: !edit_inventory,\n                                                                                onKeyDown: async (event)=>{\n                                                                                    if (event.key === \"Enter\") {\n                                                                                        const inputValue = event.target.value;\n                                                                                        await createSeaLogsFileLinks({\n                                                                                            variables: {\n                                                                                                input: {\n                                                                                                    link: inputValue\n                                                                                                }\n                                                                                            }\n                                                                                        });\n                                                                                        event.target.value = \"\";\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                                lineNumber: 980,\n                                                                                columnNumber: 53\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                            lineNumber: 977,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap gap-2\",\n                                                                            children: linkSelectedOption ? linkSelectedOption.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: linkItem(link)\n                                                                                }, link.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                                    lineNumber: 1020,\n                                                                                    columnNumber: 67\n                                                                                }, this)) : fileLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: linkItem(link)\n                                                                                }, link.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                                    lineNumber: 1032,\n                                                                                    columnNumber: 67\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                            lineNumber: 1016,\n                                                                            columnNumber: 49\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 976,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                            lineNumber: 881,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_16__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 1048,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium mb-2\",\n                                                                children: \"Attachment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground text-sm leading-relaxed\",\n                                                                children: \"Upload things like photos of the item, plus warranty and guarantee documents or operating manuals. Add links to any online manuals or product descriptions.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 1056,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 1052,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 flex items-end\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full flex flex-col space-y-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logbook_components_CloudFlareCaptures__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    inputId: 1,\n                                                                    sectionId: inventoryID,\n                                                                    buttonType: \"button\",\n                                                                    sectionName: \"inventoryID\",\n                                                                    editable: edit_inventory\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 1078,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 1077,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                            lineNumber: 1076,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 1064,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 1051,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_16__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 1125,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium mb-2\",\n                                                                children: \"Comment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 1130,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground text-sm leading-relaxed\",\n                                                                children: \"Comments are sent to directly to a person (use @name) to send a comment to someone.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 1133,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 1129,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-2 space-y-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__.Textarea, {\n                                                            id: \"inventory-comments\",\n                                                            rows: 5,\n                                                            defaultValue: inventory === null || inventory === void 0 ? void 0 : inventory.comments,\n                                                            placeholder: \"Comments\",\n                                                            readOnly: !edit_inventory\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                            lineNumber: 1140,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 1139,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 1128,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 758,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_19__.FooterWrapper, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                size: \"sm\",\n                                variant: \"back\",\n                                onClick: ()=>router.back(),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1154,\n                                columnNumber: 21\n                            }, this),\n                            bp[\"tablet-md\"] || activeTab === \"tasks\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                size: \"sm\",\n                                variant: \"primaryOutline\",\n                                onClick: ()=>{\n                                    if (!edit_task) {\n                                        sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(\"You do not have permission to edit this section\");\n                                        return;\n                                    }\n                                    router.push(\"/maintenance/new?inventoryId=\" + inventoryID + \"&vesselId=\" + (inventory === null || inventory === void 0 ? void 0 : inventory.vesselID) + \"&redirectTo=inventory\");\n                                },\n                                children: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_24__.getResponsiveLabel)(bp.phablet, \"Create\", \"Create task/maintenance\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1161,\n                                columnNumber: 25\n                            }, this) : null,\n                            activeTab !== \"tasks\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                        size: \"sm\",\n                                        variant: \"destructive\",\n                                        onClick: confirmInventoryCrew,\n                                        children: bp.phablet ? \"Delete\" : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                            lineNumber: 1192,\n                                            columnNumber: 58\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                        lineNumber: 1188,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                        size: \"sm\",\n                                        onClick: handleSave,\n                                        children: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_24__.getResponsiveLabel)(bp.phablet, \"Update\", \"Update inventory\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true) : null\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1153,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                lineNumber: 715,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openLocationDialog,\n                setOpenDialog: setOpenLocationDialog,\n                handleCreate: ()=>handleCreateLocation({}),\n                actionText: \"Create Location\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                        children: \"Create New Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1211,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                            id: \"inventory-new-location\",\n                            type: \"text\",\n                            placeholder: \"Location\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                            lineNumber: 1213,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1212,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                            id: \"inventory-new-location-id\",\n                            type: \"text\",\n                            placeholder: \"Location ID\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                            lineNumber: 1220,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1219,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                lineNumber: 1206,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openSupplierDialog,\n                setOpenDialog: setOpenSupplierDialog,\n                handleCreate: handleCreateSupplier,\n                actionText: \"Create supplier\",\n                className: \"lg:max-w-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                        children: \"Create new supplier\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1233,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                    id: \"supplier-name\",\n                                    type: \"text\",\n                                    placeholder: \"Supplier name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 1236,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1235,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                    id: \"supplier-website\",\n                                    type: \"text\",\n                                    placeholder: \"Website\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 1243,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1242,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                    id: \"supplier-phone\",\n                                    type: \"text\",\n                                    placeholder: \"Phone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 1250,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1249,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                    id: \"supplier-email\",\n                                    type: \"email\",\n                                    placeholder: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 1257,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1256,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__.Textarea, {\n                                    id: \"supplier-address\",\n                                    rows: 4,\n                                    className: \" p-2\",\n                                    placeholder: \"Supplier address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 1264,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1263,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1234,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                lineNumber: 1227,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openCategoryDialog,\n                setOpenDialog: setOpenCategoryDialog,\n                handleCreate: handleCreateCategory,\n                actionText: \"Create Category\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                        children: \"Create new category\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1278,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                            id: \"inventory-new-category\",\n                            type: \"text\",\n                            placeholder: \"Category\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                            lineNumber: 1280,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1279,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                lineNumber: 1273,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openConfirmInventoryDeleteDialog,\n                setOpenDialog: setOpenConfirmInventoryDeleteDialog,\n                handleCreate: handleDeleteInventories,\n                variant: \"warning\",\n                actionText: \"Delete Inventory\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                        children: \"Delete Inventory\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1293,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: [\n                            \"Are you sure you want to delete \",\n                            inventory === null || inventory === void 0 ? void 0 : inventory.item,\n                            \"?\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1294,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                lineNumber: 1287,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Inventory, \"QQ+5vsR0dbiVPy9siDxLBkUMd7g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_22__.useVesselIconData,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_23__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation\n    ];\n});\n_c = Inventory;\nvar _c;\n$RefreshReg$(_c, \"Inventory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/inventory/inventory.tsx\n"));

/***/ })

});