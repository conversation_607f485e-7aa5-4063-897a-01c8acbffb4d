"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/view/page",{

/***/ "(app-pages-browser)/./src/app/ui/inventory/inventory.tsx":
/*!********************************************!*\
  !*** ./src/app/ui/inventory/inventory.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Inventory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_ui_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/ui/editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _maintenance_list_list__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ../maintenance/list/list */ \"(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\");\n/* harmony import */ var _barrel_optimize_names_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _logbook_components_CloudFlareCaptures__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ../logbook/components/CloudFlareCaptures */ \"(app-pages-browser)/./src/app/ui/logbook/components/CloudFlareCaptures.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Inventory(param) {\n    let { inventoryID, inventoryTab = \"\" } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"info\");\n    const [inventory, setInventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedCategories, setSelectedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [suppliers, setSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedSuppliers, setSelectedSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    //    const [attachments, setAttachments] = useState<any>()\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [taskCounter, setTaskCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [displayTask, setDisplayTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [openLocationDialog, setOpenLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openSupplierDialog, setOpenSupplierDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openCategoryDialog, setOpenCategoryDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openConfirmInventoryDeleteDialog, setOpenConfirmInventoryDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fileLinks, setFileLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [linkSelectedOption, setLinkSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_inventory, setEdit_inventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [delete_inventory, setDelete_inventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [view_inventory, setView_inventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_22__.useVesselIconData)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_23__.useBreakpoints)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_INVENTORY\", permissions)) {\n                setEdit_inventory(true);\n            } else {\n                setEdit_inventory(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"DELETE_INVENTORY\", permissions)) {\n                setDelete_inventory(true);\n            } else {\n                setDelete_inventory(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_INVENTORY\", permissions)) {\n                setView_inventory(true);\n            } else {\n                setView_inventory(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const handleSetInventory = (data)=>{\n        var _data_categories_nodes, _data_categories, _data_suppliers_nodes, _data_suppliers, _data_documents, _data_attachmentLinks;\n        const getContent = ()=>{\n            var _data_content;\n            const content = data.content !== \"null\" ? (_data_content = data.content) !== null && _data_content !== void 0 ? _data_content : \"\" : \"\";\n            var _data_description;\n            const description = data.description !== \"null\" ? (_data_description = data.description) !== null && _data_description !== void 0 ? _data_description : \"\" : \"\";\n            return \"\".concat(content, \" \").concat(description).trim();\n        };\n        setInventory({\n            ...data,\n            content: getContent()\n        });\n        if (inventoryTab === \"maintenance\") {\n            setDisplayTask(true);\n        }\n        setSelectedLocation({\n            label: data === null || data === void 0 ? void 0 : data.location,\n            value: 0\n        });\n        setSelectedCategories(data === null || data === void 0 ? void 0 : (_data_categories = data.categories) === null || _data_categories === void 0 ? void 0 : (_data_categories_nodes = _data_categories.nodes) === null || _data_categories_nodes === void 0 ? void 0 : _data_categories_nodes.map((category)=>({\n                label: category.name,\n                value: category.id\n            })));\n        setSelectedSuppliers(data === null || data === void 0 ? void 0 : (_data_suppliers = data.suppliers) === null || _data_suppliers === void 0 ? void 0 : (_data_suppliers_nodes = _data_suppliers.nodes) === null || _data_suppliers_nodes === void 0 ? void 0 : _data_suppliers_nodes.map((supplier)=>({\n                label: supplier.name,\n                value: supplier.id\n            })));\n        queryMaintenanceCheck({\n            variables: {\n                inventoryID: +inventoryID,\n                vesselID: 0\n            }\n        });\n        setDocuments(data === null || data === void 0 ? void 0 : (_data_documents = data.documents) === null || _data_documents === void 0 ? void 0 : _data_documents.nodes);\n        setLinkSelectedOption(data === null || data === void 0 ? void 0 : (_data_attachmentLinks = data.attachmentLinks) === null || _data_attachmentLinks === void 0 ? void 0 : _data_attachmentLinks.nodes.map((link)=>({\n                label: link.link,\n                value: link.id\n            })));\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getInventoryByID)(inventoryID, handleSetInventory);\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const vesselList = activeVessels.map((item)=>({\n                ...item\n            }));\n        const appendedData = [\n            // { title: '-- Other --', id: 'newLocation' },\n            ...vesselList,\n            {\n                title: \"Other\",\n                id: \"0\"\n            }\n        ];\n        setVessels(appendedData);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getVesselList)(handleSetVessels);\n    const handelSetSuppliers = (data)=>{\n        const suppliersList = [\n            {\n                label: \" ---- Create supplier ---- \",\n                value: \"newSupplier\",\n                excludeFromSelectAll: true\n            },\n            ...data === null || data === void 0 ? void 0 : data.filter((supplier)=>supplier.name !== null).map((supplier)=>({\n                    label: supplier.name,\n                    value: supplier.id\n                }))\n        ];\n        setSuppliers(suppliersList);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getSupplier)(handelSetSuppliers);\n    const handleSetCategories = (data)=>{\n        const formattedData = [\n            {\n                label: \" ---- Create Category ---- \",\n                value: \"newCategory\",\n                excludeFromSelectAll: true\n            },\n            ...data === null || data === void 0 ? void 0 : data.filter((category)=>category.name !== null && category.archived === false).map((category)=>({\n                    label: category.name,\n                    value: category.id\n                }))\n        ];\n        setCategories(formattedData);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getInventoryCategory)(handleSetCategories);\n    const [queryMaintenanceCheck] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_CHECK_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].list;\n            if (data) {\n                const activeTasks = data.filter((task)=>(task === null || task === void 0 ? void 0 : task.archived) != 1);\n                setTasks(data);\n                const taskCounter = activeTasks.filter((task)=>task.isOverDue.status === \"High\").length;\n                setTaskCounter(taskCounter);\n                const appendedData = Array.from(new Set(data.filter((item)=>item.assignedToID > 0).map((item)=>item.assignedToID)));\n                loadCrewMemberInfo(appendedData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceCheck error\", error);\n        }\n    });\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleSetSelectedCategories = (selectedOption)=>{\n        if (selectedOption.find((option)=>option.value === \"newCategory\")) {\n            setOpenCategoryDialog(true);\n        }\n        setSelectedCategories(selectedOption.filter((option)=>option.value !== \"newCategory\"));\n    };\n    const handleEditorChange = (content)=>{\n        setInventory({\n            ...inventory,\n            content: content\n        });\n    };\n    const handleSave = async ()=>{\n        var _inventory_attachmentLinks;\n        if (!inventory) {\n            console.error(\"Inventory page has not been initialised, possibly a slow internet connection, please try after a few seconds\");\n            sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(\"Please wait to initialize the inventory before saving\");\n            return;\n        }\n        if (!edit_inventory) {\n            sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(\"You do not have permission to edit this inventory\");\n            return;\n        }\n        const variables = {\n            input: {\n                id: +inventory.id,\n                item: document.getElementById(\"inventory-name\").value ? document.getElementById(\"inventory-name\").value : inventory.item,\n                title: document.getElementById(\"inventory-name\").value ? document.getElementById(\"inventory-name\").value : inventory.title,\n                location: document.getElementById(\"inventory-location\").value ? document.getElementById(\"inventory-location\").value : inventory.location,\n                description: null,\n                content: inventory.content,\n                quantity: document.getElementById(\"inventory-qty\").value ? parseInt(document.getElementById(\"inventory-qty\").value) : inventory.quantity,\n                productCode: document.getElementById(\"inventory-code\").value ? document.getElementById(\"inventory-code\").value : inventory.productCode,\n                costingDetails: document.getElementById(\"inventory-cost\").value ? document.getElementById(\"inventory-cost\").value : inventory.costingDetails,\n                comments: document.getElementById(\"inventory-comments\").value ? document.getElementById(\"inventory-comments\").value : inventory.comments,\n                archived: inventory.archived,\n                inventoryImportID: inventory.inventoryImportID,\n                vesselID: selectedLocation.value ? selectedLocation.value : inventory.vesselID,\n                // attachments: inventory.attachments,\n                documents: documents.map((doc)=>doc.id).join(\",\"),\n                categories: (selectedCategories === null || selectedCategories === void 0 ? void 0 : selectedCategories.map((category)=>category.value).length) ? selectedCategories.map((category)=>category.value).join(\",\") : inventory.categories.nodes.map((categories)=>categories.id).join(\",\"),\n                suppliers: (selectedSuppliers === null || selectedSuppliers === void 0 ? void 0 : selectedSuppliers.map((supplier)=>supplier.value).length) ? selectedSuppliers.map((supplier)=>supplier.value).join(\",\") : inventory.suppliers.nodes.map((supplier)=>supplier.id).join(\",\"),\n                attachmentLinks: linkSelectedOption ? linkSelectedOption.map((link)=>link.value).join(\",\") : (_inventory_attachmentLinks = inventory.attachmentLinks) === null || _inventory_attachmentLinks === void 0 ? void 0 : _inventory_attachmentLinks.nodes.map((link)=>link.id).join(\",\")\n            }\n        };\n        await mutationUpdateInventory({\n            variables\n        });\n    };\n    const [mutationUpdateInventory, { loading: mutationupdateInventoryLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_5__.UPDATE_INVENTORY, {\n        onCompleted: (response)=>{\n            const data = response.updateInventory;\n            if (data.id > 0) {\n                searchParams.get(\"redirect_to\") ? router.push((searchParams === null || searchParams === void 0 ? void 0 : searchParams.get(\"redirect_to\")) + \"\") : router.back();\n            } else {\n                console.error(\"mutationupdateInventory error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationupdateInventory error\", error);\n        }\n    });\n    const handleCreateCategory = async ()=>{\n        const categoryName = document.getElementById(\"inventory-new-category\").value;\n        return await mutationcreateInventoryCategory({\n            variables: {\n                input: {\n                    name: categoryName\n                }\n            }\n        });\n    };\n    const [mutationcreateInventoryCategory, { loading: mutationcreateInventoryCategoryLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_5__.CREATE_INVENTORY_CATEGORY, {\n        onCompleted: (response)=>{\n            const data = response.createInventoryCategory;\n            if (data.id > 0) {\n                const formattedData = [\n                    ...categories,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setCategories(formattedData);\n                const categoriesList = [\n                    ...selectedCategories,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSelectedCategories(categoriesList);\n                setOpenCategoryDialog(false);\n            } else {\n                console.error(\"mutationcreateInventoryCategory error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateInventoryCategory error\", error);\n        }\n    });\n    const handleDeleteInventories = async ()=>{\n        if (!delete_inventory) {\n            sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(\"You do not have permission to delete this inventory\");\n            return;\n        }\n        await mutationDeleteInventories({\n            variables: {\n                ids: [\n                    +inventory.id\n                ]\n            }\n        });\n    };\n    const [mutationDeleteInventories, { loading: mutationdeleteInventoriesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_5__.DELETE_INVENTORIES, {\n        onCompleted: (response)=>{\n            if (response.deleteInventories && response.deleteInventories.length > 0) {\n                router.push(\"/inventory\");\n            } else {\n                console.error(\"mutationdeleteInventories failed to delete:\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationdeleteInventories error:\", error.message);\n        }\n    });\n    const handleSelectedVesselChange = (selectedOption)=>{\n        if (selectedOption && selectedOption.value === \"newLocation\") {\n            setOpenLocationDialog(true);\n        }\n        setSelectedLocation(selectedOption);\n    };\n    const handleCreateLocation = (Location)=>{\n        var newLocation = {\n            label: \"\",\n            value: \"\"\n        };\n        if (typeof Location === \"string\") {\n            newLocation = {\n                label: Location,\n                value: Location\n            };\n        }\n        if (typeof Location === \"object\") {\n            newLocation = {\n                label: document.getElementById(\"inventory-new-location\").value,\n                value: document.getElementById(\"inventory-new-location-id\").value ? document.getElementById(\"inventory-new-location-id\").value : document.getElementById(\"inventory-new-location\").value\n            };\n        }\n        const vesselList = vessels.map((item)=>({\n                ...item\n            }));\n        const appendedData = [\n            ...vesselList,\n            {\n                Title: newLocation.label,\n                ID: newLocation.value\n            }\n        ];\n        setVessels(appendedData);\n        setSelectedLocation(newLocation);\n        setOpenLocationDialog(false);\n    };\n    const deleteFile = async (id)=>{\n        const newDocuments = documents.filter((doc)=>doc.id !== id);\n        setDocuments(newDocuments);\n    };\n    const handleDisplayTask = ()=>{\n        setDisplayTask(true);\n    };\n    const handleSelectedSuppliers = (selectedOption)=>{\n        if (selectedOption.find((option)=>option.value === \"newSupplier\")) {\n            setOpenSupplierDialog(true);\n        }\n        setSelectedSuppliers(selectedOption.filter((option)=>option.value !== \"newSupplier\"));\n    };\n    const handleCreateSupplier = async ()=>{\n        const name = document.getElementById(\"supplier-name\").value;\n        const website = document.getElementById(\"supplier-website\").value;\n        const phone = document.getElementById(\"supplier-phone\").value;\n        const email = document.getElementById(\"supplier-email\").value;\n        const address = document.getElementById(\"supplier-address\").value;\n        const variables = {\n            input: {\n                name: name,\n                address: address,\n                website: website,\n                email: email,\n                phone: phone\n            }\n        };\n        if (name !== \"\") {\n            await mutationCreateSupplier({\n                variables\n            });\n        }\n        setOpenSupplierDialog(false);\n    };\n    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_5__.CREATE_SUPPLIER, {\n        onCompleted: (response)=>{\n            const data = response.createSupplier;\n            if (data.id > 0) {\n                const suppliersList = [\n                    ...suppliers,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSuppliers(suppliersList);\n                const selectedSuppliersList = [\n                    ...selectedSuppliers,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSelectedSuppliers(selectedSuppliersList);\n            } else {\n                console.error(\"mutationcreateSupplier error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplier error\", error);\n        }\n    });\n    const [createSeaLogsFileLinks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_5__.CREATE_SEALOGS_FILE_LINKS, {\n        onCompleted: (response)=>{\n            const data = response.createSeaLogsFileLinks;\n            if (data.id > 0) {\n                const newLinks = [\n                    ...fileLinks,\n                    data\n                ];\n                setFileLinks(newLinks);\n                linkSelectedOption ? setLinkSelectedOption([\n                    ...linkSelectedOption,\n                    {\n                        label: data.link,\n                        value: data.id\n                    }\n                ]) : setLinkSelectedOption([\n                    {\n                        label: data.link,\n                        value: data.id\n                    }\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createSeaLogsFileLinksEntry error\", error);\n        }\n    });\n    const handleDeleteLink = (link)=>{\n        setLinkSelectedOption(linkSelectedOption.filter((l)=>l !== link));\n    };\n    const linkItem = (link)=>{\n        if (!link.label) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between align-middle mr-2 w-fit\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    href: link.label,\n                    target: \"_blank\",\n                    className: \"ml-2 \",\n                    children: link.label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                    lineNumber: 685,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-2 \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"destructive\",\n                        iconOnly: true,\n                        iconLeft: _barrel_optimize_names_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n                        onClick: ()=>handleDeleteLink(link)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 689,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                    lineNumber: 688,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n            lineNumber: 684,\n            columnNumber: 13\n        }, this);\n    };\n    if (!permissions || !view_inventory) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n            lineNumber: 702,\n            columnNumber: 13\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n            lineNumber: 704,\n            columnNumber: 13\n        }, this);\n    }\n    const confirmInventoryCrew = ()=>{\n        if (inventory) {\n            setOpenConfirmInventoryDeleteDialog(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"text-2xl font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-muted-foreground mr-2\",\n                                    children: \"Inventory:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 25\n                                }, this),\n                                inventory === null || inventory === void 0 ? void 0 : inventory.item\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                            lineNumber: 718,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 717,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_16__.Separator, {\n                        className: \"mb-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.Tabs, {\n                            defaultValue: \"info\",\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsTrigger, {\n                                            value: \"info\",\n                                            children: \"Item Info\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsTrigger, {\n                                            value: \"tasks\",\n                                            className: \"relative\",\n                                            children: [\n                                                \"Tasks / Maintenance\",\n                                                taskCounter > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_17__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"ml-2 h-4 \".concat(taskCounter > 0 ? \"bg-rose-100 text-rose-700 hover:bg-rose-200\" : \"bg-emerald-100 text-emerald-700 hover:bg-emerald-200\"),\n                                                    children: taskCounter\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsContent, {\n                                    value: \"tasks\",\n                                    className: \"pt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full\",\n                                        children: tasks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_maintenance_list_list__WEBPACK_IMPORTED_MODULE_21__.MaintenanceTable, {\n                                            maintenanceChecks: tasks,\n                                            vessels: vessels,\n                                            crewInfo: crewInfo\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 37\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsContent, {\n                                    value: \"info\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6 py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid sm:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                id: \"inventory-name\",\n                                                                type: \"text\",\n                                                                defaultValue: inventory === null || inventory === void 0 ? void 0 : inventory.item,\n                                                                placeholder: \"Inventory name\",\n                                                                readOnly: !edit_inventory\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            vessels && inventory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_13__.Combobox, {\n                                                                id: \"inventory-vessel\",\n                                                                options: vessels === null || vessels === void 0 ? void 0 : vessels.map((vessel)=>{\n                                                                    const vesselWithIcon = getVesselWithIcon(vessel.id, vessel);\n                                                                    return {\n                                                                        label: vessel.title,\n                                                                        value: vessel.id,\n                                                                        vessel: vesselWithIcon\n                                                                    };\n                                                                }),\n                                                                isDisabled: !edit_inventory,\n                                                                defaultValues: (inventory === null || inventory === void 0 ? void 0 : inventory.vesselID) && (inventory === null || inventory === void 0 ? void 0 : inventory.vesselID) == 0 ? {\n                                                                    label: \"Other\",\n                                                                    value: \"0\"\n                                                                } : (inventory === null || inventory === void 0 ? void 0 : inventory.vessel) ? {\n                                                                    label: inventory.vessel.title,\n                                                                    value: inventory.vessel.id,\n                                                                    vessel: getVesselWithIcon(inventory.vessel.id, inventory.vessel)\n                                                                } : null,\n                                                                className: \"w-full\",\n                                                                placeholder: \"Select Vessel\",\n                                                                onChange: handleSelectedVesselChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 45\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_8__.InputSkeleton, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 819,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid sm:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                id: \"inventory-location\",\n                                                                type: \"text\",\n                                                                defaultValue: inventory === null || inventory === void 0 ? void 0 : inventory.location,\n                                                                placeholder: \"Location\",\n                                                                readOnly: !edit_inventory\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                id: \"inventory-qty\",\n                                                                type: \"number\",\n                                                                defaultValue: inventory === null || inventory === void 0 ? void 0 : inventory.quantity,\n                                                                placeholder: \"Quantity\",\n                                                                readOnly: !edit_inventory\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium mb-2\",\n                                                                children: \"Description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 843,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground text-sm leading-relaxed\",\n                                                                children: \"Enter details that might help with the maintenance or operation of this item.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 846,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 842,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-2 space-y-4\",\n                                                        children: inventory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_editor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            id: \"inventory-Content\",\n                                                            content: inventory === null || inventory === void 0 ? void 0 : inventory.content,\n                                                            handleEditorChange: handleEditorChange\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_16__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium mb-2\",\n                                                                children: \"Inventory details\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 870,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground text-sm leading-relaxed\",\n                                                                children: \"In this section categorise the item and add the suppliers where you normally purchase this item and the expected cost. This will help replacing the item in the future.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 873,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                            className: \"space-y-5\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                                                                    label: \"Product code\",\n                                                                    htmlFor: \"inventory-code\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                        id: \"inventory-code\",\n                                                                        type: \"text\",\n                                                                        defaultValue: inventory === null || inventory === void 0 ? void 0 : inventory.productCode,\n                                                                        placeholder: \"Product code\",\n                                                                        readOnly: !edit_inventory\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                        lineNumber: 886,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 883,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                inventory && categories ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_13__.Combobox, {\n                                                                    className: \"w-full\",\n                                                                    label: \"Categories\",\n                                                                    id: \"inventory-categories\",\n                                                                    defaultValues: inventory.categories && inventory.categories.nodes.map((category)=>({\n                                                                            label: category.name,\n                                                                            value: category.id\n                                                                        })),\n                                                                    isDisabled: !edit_inventory,\n                                                                    value: selectedCategories,\n                                                                    multi: true,\n                                                                    options: categories,\n                                                                    onChange: handleSetSelectedCategories\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 49\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_8__.InputSkeleton, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 921,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                inventory && suppliers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_13__.Combobox, {\n                                                                    label: \"Supplier\",\n                                                                    className: \"w-full\",\n                                                                    id: \"inventory-suppliers\",\n                                                                    defaultValues: inventory.Suppliers && (suppliers === null || suppliers === void 0 ? void 0 : suppliers.filter((supplier)=>{\n                                                                        var _supplier_value;\n                                                                        return (inventory === null || inventory === void 0 ? void 0 : inventory.Suppliers) && Object.keys(inventory.Suppliers).includes(supplier === null || supplier === void 0 ? void 0 : (_supplier_value = supplier.value) === null || _supplier_value === void 0 ? void 0 : _supplier_value.toString());\n                                                                    }).map((supplier)=>({\n                                                                            label: supplier.label,\n                                                                            value: supplier.value\n                                                                        }))),\n                                                                    multi: true,\n                                                                    isDisabled: !edit_inventory,\n                                                                    value: selectedSuppliers,\n                                                                    onChange: handleSelectedSuppliers,\n                                                                    options: suppliers\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 925,\n                                                                    columnNumber: 49\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_8__.InputSkeleton, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 961,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                                                                    label: \"Cost\",\n                                                                    htmlFor: \"inventory-cost\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                        id: \"inventory-cost\",\n                                                                        type: \"text\",\n                                                                        defaultValue: inventory === null || inventory === void 0 ? void 0 : inventory.costingDetails,\n                                                                        placeholder: \"Costing details\",\n                                                                        readOnly: !edit_inventory\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                        lineNumber: 967,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 964,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                                                                            label: \"Links\",\n                                                                            htmlFor: \"task-title\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                id: \"task-title\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Type a link and press Enter\",\n                                                                                readOnly: !edit_inventory,\n                                                                                onKeyDown: async (event)=>{\n                                                                                    if (event.key === \"Enter\") {\n                                                                                        const inputValue = event.target.value;\n                                                                                        await createSeaLogsFileLinks({\n                                                                                            variables: {\n                                                                                                input: {\n                                                                                                    link: inputValue\n                                                                                                }\n                                                                                            }\n                                                                                        });\n                                                                                        event.target.value = \"\";\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                                lineNumber: 981,\n                                                                                columnNumber: 53\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                            lineNumber: 978,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap gap-2\",\n                                                                            children: linkSelectedOption ? linkSelectedOption.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: linkItem(link)\n                                                                                }, link.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                                    lineNumber: 1021,\n                                                                                    columnNumber: 67\n                                                                                }, this)) : fileLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: linkItem(link)\n                                                                                }, link.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                                    lineNumber: 1033,\n                                                                                    columnNumber: 67\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                            lineNumber: 1017,\n                                                                            columnNumber: 49\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 977,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 868,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_16__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 1049,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium mb-2\",\n                                                                children: \"Attachment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 1054,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground text-sm leading-relaxed\",\n                                                                children: \"Upload things like photos of the item, plus warranty and guarantee documents or operating manuals. Add links to any online manuals or product descriptions.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 1057,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 1053,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 flex items-end\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full flex flex-col space-y-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logbook_components_CloudFlareCaptures__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    inputId: 1,\n                                                                    sectionId: inventoryID,\n                                                                    buttonType: \"button\",\n                                                                    sectionName: \"inventoryID\",\n                                                                    editable: edit_inventory\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                    lineNumber: 1079,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 1078,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                            lineNumber: 1077,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 1065,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 1052,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_16__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 1126,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium mb-2\",\n                                                                children: \"Comment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 1131,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground text-sm leading-relaxed\",\n                                                                children: \"Comments are sent to directly to a person (use @name) to send a comment to someone.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                                lineNumber: 1134,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 1130,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-2 space-y-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__.Textarea, {\n                                                            id: \"inventory-comments\",\n                                                            rows: 5,\n                                                            defaultValue: inventory === null || inventory === void 0 ? void 0 : inventory.comments,\n                                                            placeholder: \"Comments\",\n                                                            readOnly: !edit_inventory\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                            lineNumber: 1141,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                        lineNumber: 1140,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                                lineNumber: 1129,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_19__.FooterWrapper, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                size: \"sm\",\n                                variant: \"back\",\n                                onClick: ()=>router.back(),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1155,\n                                columnNumber: 21\n                            }, this),\n                            bp[\"tablet-md\"] || activeTab === \"tasks\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                size: \"sm\",\n                                variant: \"primaryOutline\",\n                                onClick: ()=>{\n                                    if (!edit_task) {\n                                        sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(\"You do not have permission to edit this section\");\n                                        return;\n                                    }\n                                    router.push(\"/maintenance/new?inventoryId=\" + inventoryID + \"&vesselId=\" + (inventory === null || inventory === void 0 ? void 0 : inventory.vesselID) + \"&redirectTo=inventory\");\n                                },\n                                children: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_24__.getResponsiveLabel)(bp.phablet, \"Create\", \"Create task/maintenance\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1162,\n                                columnNumber: 25\n                            }, this) : null,\n                            activeTab !== \"tasks\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                        size: \"sm\",\n                                        variant: \"destructive\",\n                                        onClick: confirmInventoryCrew,\n                                        children: bp.phablet ? \"Delete\" : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                            lineNumber: 1193,\n                                            columnNumber: 58\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                        lineNumber: 1189,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                        size: \"sm\",\n                                        onClick: handleSave,\n                                        children: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_24__.getResponsiveLabel)(bp.phablet, \"Update\", \"Update inventory\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                        lineNumber: 1195,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true) : null\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1154,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                lineNumber: 716,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openLocationDialog,\n                setOpenDialog: setOpenLocationDialog,\n                handleCreate: ()=>handleCreateLocation({}),\n                actionText: \"Create Location\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                        children: \"Create New Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1212,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                            id: \"inventory-new-location\",\n                            type: \"text\",\n                            placeholder: \"Location\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                            lineNumber: 1214,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1213,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                            id: \"inventory-new-location-id\",\n                            type: \"text\",\n                            placeholder: \"Location ID\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                            lineNumber: 1221,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1220,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                lineNumber: 1207,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openSupplierDialog,\n                setOpenDialog: setOpenSupplierDialog,\n                handleCreate: handleCreateSupplier,\n                actionText: \"Create supplier\",\n                className: \"lg:max-w-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                        children: \"Create new supplier\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1234,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                    id: \"supplier-name\",\n                                    type: \"text\",\n                                    placeholder: \"Supplier name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 1237,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1236,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                    id: \"supplier-website\",\n                                    type: \"text\",\n                                    placeholder: \"Website\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 1244,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1243,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                    id: \"supplier-phone\",\n                                    type: \"text\",\n                                    placeholder: \"Phone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 1251,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1250,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                    id: \"supplier-email\",\n                                    type: \"email\",\n                                    placeholder: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 1258,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1257,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__.Textarea, {\n                                    id: \"supplier-address\",\n                                    rows: 4,\n                                    className: \" p-2\",\n                                    placeholder: \"Supplier address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                    lineNumber: 1265,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                                lineNumber: 1264,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1235,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                lineNumber: 1228,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openCategoryDialog,\n                setOpenDialog: setOpenCategoryDialog,\n                handleCreate: handleCreateCategory,\n                actionText: \"Create Category\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                        children: \"Create new category\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1279,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                            id: \"inventory-new-category\",\n                            type: \"text\",\n                            placeholder: \"Category\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                            lineNumber: 1281,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1280,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                lineNumber: 1274,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openConfirmInventoryDeleteDialog,\n                setOpenDialog: setOpenConfirmInventoryDeleteDialog,\n                handleCreate: handleDeleteInventories,\n                variant: \"warning\",\n                actionText: \"Delete Inventory\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                        children: \"Delete Inventory\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1294,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: [\n                            \"Are you sure you want to delete \",\n                            inventory === null || inventory === void 0 ? void 0 : inventory.item,\n                            \"?\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                        lineNumber: 1295,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory.tsx\",\n                lineNumber: 1288,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Inventory, \"QQ+5vsR0dbiVPy9siDxLBkUMd7g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_22__.useVesselIconData,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_23__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation\n    ];\n});\n_c = Inventory;\nvar _c;\n$RefreshReg$(_c, \"Inventory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/inventory/inventory.tsx\n"));

/***/ })

});