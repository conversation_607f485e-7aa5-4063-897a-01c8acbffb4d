'use client'

import * as React from 'react'
import { Check, PlusCircle, ChevronDown, X } from 'lucide-react'
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { cn } from '@/app/lib/utils'
import { Skeleton } from '@/components/ui/skeleton'
import {
    Avatar,
    AvatarFallback,
    AvatarImage,
    getCrewInitials,
} from '@/components/ui/avatar'
import { Label, LabelPosition } from './label'
import VesselIcon from '@/app/ui/vessels/vesel-icon'
import { useResponsiveBadges } from '@/hooks/useResponsiveBadges'

/* -------------------------------------------------------------------------- */
/* Types                                                                      */
/* -------------------------------------------------------------------------- */

export type Option = {
    value: string
    label: string | number | null
    profile?: {
        avatar?: string
        firstName?: string
        surname?: string | null
    }
    vessel?: {
        id?: string | number
        title?: string
        icon?: string
        iconMode?: 'Photo' | 'Icon'
        photoID?: string
    }
    className?: string
    excludeFromSelectAll?: boolean
}

interface ComboboxProps
    extends Omit<
        React.ButtonHTMLAttributes<HTMLButtonElement>,
        'onChange' | 'value' | 'defaultValue'
    > {
    options: Option[]
    title?: string
    value?: Option | Option[] | null
    defaultValues?: Option | Option[] | null
    onChange: (option: Option | Option[] | null) => void
    placeholder?: string
    buttonClassName?: string
    multi?: boolean
    isDisabled?: boolean
    isLoading?: boolean
    label?: string
    labelPosition?: LabelPosition
    labelClassName?: string
    searchThreshold?: number
    required?: boolean
    noResultsMessage?: string
    searchPlaceholder?: string
    groupBy?: (option: Option) => string
    selectAllLabel?: string
    badgeLimit?: number
    wrapBadges?: boolean
    responsiveBadges?: boolean
    modal?: boolean
    align?: 'start' | 'center' | 'end'
}

/* -------------------------------------------------------------------------- */
/* Controlled / uncontrolled helper                                           */
/* -------------------------------------------------------------------------- */

function useControlled<T>(controlled: T | undefined, defaultValue: T) {
    const [state, setState] = React.useState(defaultValue)
    const value = controlled !== undefined ? controlled : state
    return [value, setState] as [T, React.Dispatch<React.SetStateAction<T>>]
}

/* -------------------------------------------------------------------------- */
/* Avatar helper                                                              */
/* -------------------------------------------------------------------------- */

const OptionAvatar = React.memo(function OptionAvatar({
    profile,
    vessel,
    label,
}: {
    profile?: Option['profile']
    vessel?: Option['vessel']
    label?: Option['label']
}) {
    // Show vessel icon if vessel data is present
    if (vessel) {
        return (
            <div className="size-7 flex items-center justify-center flex-shrink-0 [&_img]:!size-6 [&_svg]:!size-6">
                <VesselIcon vessel={vessel} />
            </div>
        )
    }

    // Show crew avatar if profile data is present
    if (profile) {
        const initials =
            getCrewInitials(profile.firstName, profile.surname ?? '') ??
            String(label).charAt(0)
        return (
            <Avatar size="xs">
                <AvatarImage src={profile.avatar} alt={String(label)} />
                <AvatarFallback>{initials}</AvatarFallback>
            </Avatar>
        )
    }

    return null
})

/* -------------------------------------------------------------------------- */
/* Main component                                                             */
/* -------------------------------------------------------------------------- */

export const Combobox = ({
    options,
    title,
    value,
    defaultValues,
    onChange,
    placeholder = 'Select an option',
    buttonClassName = '',
    multi = false,
    isDisabled = false,
    isLoading = false,
    label,
    labelPosition = 'top',
    required = false,
    labelClassName = '',
    searchThreshold = 8,
    noResultsMessage = 'No results found.',
    searchPlaceholder = 'Search...',
    groupBy,
    selectAllLabel = 'Select all',
    badgeLimit = 2,
    wrapBadges = false,
    responsiveBadges = true,
    modal = false,
    align = 'start',
    ...buttonProps
}: ComboboxProps) => {
    const comboboxId = React.useId()
    const [open, setOpen] = React.useState(false)
    const [searchQuery, setSearchQuery] = React.useState('')
    const [activeItem, setActiveItem] = React.useState<string | null>(null)
    const badgeContainerRef = React.useRef<HTMLDivElement>(null)

    /* ----------------------------------------------------------------------- */
    /* Controlled / uncontrolled                                               */
    /* ----------------------------------------------------------------------- */
    const [currentValue, setCurrentValue] = useControlled<
        Option | Option[] | null
    >(
        value,
        multi
            ? (defaultValues as Option[]) || []
            : (defaultValues as Option) || null,
    )

    /* ----------------------------------------------------------------------- */
    /* Filtering & grouping                                                    */
    /* ----------------------------------------------------------------------- */
    const filteredOptions = React.useMemo(() => {
        if (!searchQuery) return options
        const q = searchQuery.toLowerCase()
        return options.filter((opt) => {
            const lbl = String(opt.label ?? '').toLowerCase()
            return (
                lbl.includes(q) || lbl.split(' ').some((w) => w.startsWith(q))
            )
        })
    }, [options, searchQuery])

    const groupedOptions = React.useMemo(() => {
        if (!groupBy) return { ungrouped: filteredOptions }
        return filteredOptions.reduce<Record<string, Option[]>>((acc, opt) => {
            const key = groupBy(opt) || 'Other'
            ;(acc[key] = acc[key] || []).push(opt)
            return acc
        }, {})
    }, [filteredOptions, groupBy])

    /* ----------------------------------------------------------------------- */
    /* Badge logic                                                             */
    /* ----------------------------------------------------------------------- */

    // Use responsive badges hook when enabled
    const responsiveBadgesResult = useResponsiveBadges({
        badges: Array.isArray(currentValue) ? currentValue : [],
        enabled: responsiveBadges && multi && !wrapBadges,
        fallbackLimit: badgeLimit,
        containerRef: badgeContainerRef,
    })

    const [visibleBadges, hiddenCount] = React.useMemo(() => {
        if (!multi || !Array.isArray(currentValue) || currentValue.length === 0)
            return [[], 0] as [Option[], number]

        if (wrapBadges) return [currentValue, 0]

        // Use responsive badges when enabled
        if (responsiveBadges) {
            return [
                responsiveBadgesResult.visibleBadges,
                responsiveBadgesResult.hiddenCount,
            ]
        }

        // Fallback to static badge limit
        const limit = Math.max(badgeLimit, 0)
        const visible = currentValue.slice(0, limit)
        return [visible, currentValue.length - visible.length]
    }, [
        currentValue,
        multi,
        badgeLimit,
        wrapBadges,
        responsiveBadges,
        responsiveBadgesResult,
    ])

    /* ----------------------------------------------------------------------- */
    /* Helpers                                                                 */
    /* ----------------------------------------------------------------------- */
    const isSelected = React.useCallback(
        (opt: Option) => {
            if (multi) {
                return (
                    Array.isArray(currentValue) &&
                    currentValue.some((c) => c.value === opt.value)
                )
            }
            return (currentValue as Option)?.value === opt.value
        },
        [currentValue, multi],
    )

    const updateBadges = React.useCallback(() => {
        setSearchQuery((q) => q) // force re-render
    }, [])

    const handleSelect = React.useCallback(
        (selectedValue: string) => {
            /* -- “Select All” ---------------------------------------------------- */
            if (multi && selectedValue === 'select-all') {
                const currentArr = Array.isArray(currentValue)
                    ? [...currentValue]
                    : []
                // Filter out options that should be excluded from "Select All"
                const selectableOptions = filteredOptions.filter(
                    (option) => !option.excludeFromSelectAll,
                )

                const allSelectableSelected = selectableOptions.every((f) =>
                    currentArr.some((c) => c.value === f.value),
                )

                const newVals: Option[] = allSelectableSelected
                    ? // Deselect all selectable options
                      currentArr.filter(
                          (c) =>
                              !selectableOptions.some(
                                  (f) => f.value === c.value,
                              ),
                      )
                    : // Select all selectable options (preserve non-selectable ones)
                      [
                          ...currentArr.filter(
                              (c) =>
                                  !selectableOptions.some(
                                      (f) => f.value === c.value,
                                  ),
                          ),
                          ...selectableOptions,
                      ]

                setCurrentValue(newVals)
                onChange(newVals)
                updateBadges()
                return
            }

            /* -- Regular selection ---------------------------------------------- */
            const opt = options.find((o) => o.value === selectedValue)
            if (!opt) return

            if (multi) {
                const curr = Array.isArray(currentValue)
                    ? [...currentValue]
                    : []
                const idx = curr.findIndex((c) => c.value === opt.value)
                const newArr =
                    idx >= 0
                        ? [...curr.slice(0, idx), ...curr.slice(idx + 1)]
                        : [...curr, opt]
                setCurrentValue(newArr)
                onChange(newArr)
                updateBadges()
            } else {
                const newVal =
                    (currentValue as Option)?.value === opt.value ? null : opt
                setCurrentValue(newVal)
                onChange(newVal)
                setOpen(false)
            }
        },
        [multi, currentValue, filteredOptions, options, onChange, updateBadges],
    )

    const handleBadgeRemove = React.useCallback(
        (value: string) => {
            const newArr = (currentValue as Option[]).filter(
                (i) => i.value !== value,
            )
            setCurrentValue(newArr)
            onChange(newArr)
            updateBadges()
        },
        [currentValue, onChange, updateBadges],
    )

    /* Reset search on popover close ----------------------------------------- */
    React.useEffect(() => {
        if (!open) {
            setSearchQuery('')
            setActiveItem(null)
        }
    }, [open])

    React.useEffect(() => {
        if (value !== undefined) {
            setCurrentValue(value)
        }
    }, [value])

    /* Screen reader text ---------------------------------------------------- */
    const selectedOptionsText = React.useMemo(() => {
        if (!multi || !Array.isArray(currentValue) || currentValue.length === 0)
            return ''
        return `Selected options: ${currentValue.map((o) => o.label ?? 'Unknown').join(', ')}`
    }, [multi, currentValue])

    /* ----------------------------------------------------------------------- */
    /* Renderers                                                               */
    /* ----------------------------------------------------------------------- */

    const renderComboboxButton = () => (
        <Button
            id={comboboxId}
            disabled={isDisabled}
            aria-required={required}
            variant="outline"
            asInput
            data-wrap={wrapBadges ? 'true' : undefined}
            className={cn(
                'justify-between shadow-none font-normal h-13 flex-1 px-4 bg-card text-input transition-colors',
                'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                wrapBadges
                    ? 'items-start min-h-[43px] h-fit py-3'
                    : 'items-center justify-between max-h-[43px]',
                buttonClassName,
            )}
            aria-expanded={open}
            aria-haspopup="listbox"
            aria-describedby={`${comboboxId}-sr`}
            iconLeft={
                multi && (
                    <PlusCircle className="flex-shrink-0 size-3 text-outer-space-400" />
                )
            }
            iconRight={
                <ChevronDown size={20} className="text-outer-space-400" />
            }
            {...buttonProps}>
            <div className="w-full flex flex-col overflow-hidden gap-2.5 min-w-0">
                {multi ? (
                    Array.isArray(currentValue) && currentValue.length > 0 ? (
                        <div className="w-full grid border-l border-border ms-0.5 ps-1.5 pe-0.5 gap-2.5">
                            {/* Visible / scrollable badge row -------------------------------- */}
                            <div
                                ref={badgeContainerRef}
                                className={cn(
                                    'flex gap-1',
                                    wrapBadges
                                        ? 'flex-wrap'
                                        : 'flex-nowrap flex-1 overflow-hidden',
                                )}>
                                {visibleBadges.map((opt) => (
                                    <Badge
                                        key={opt.value}
                                        variant="outline"
                                        title={String(opt.label ?? '')}
                                        className={cn(
                                            'rounded-md min-w-28 font-normal px-1.5 py-0.5 flex flex-1 max-w-fit items-center gap-1 bg-card transition-colors flex-shrink-0',
                                            wrapBadges
                                                ? 'h-fit w-fit'
                                                : 'h-full min-w-min overflow-auto',
                                        )}>
                                        <div className="flex flex-1 items-center overflow-auto gap-2.5">
                                            <OptionAvatar
                                                profile={opt.profile}
                                                vessel={opt.vessel}
                                                label={opt.label}
                                            />
                                            <div className="grid">
                                                <span className="text-base leading-5 max-w-40 truncate text-input">
                                                    {opt.label ?? ''}
                                                </span>
                                            </div>
                                        </div>
                                        <div
                                            className="ml-1 flex items-center text-outer-space-400/50 hover:text-outer-space-400 justify-center"
                                            onClick={(e) => {
                                                e.stopPropagation()
                                                handleBadgeRemove(opt.value)
                                            }}
                                            aria-label={`Remove ${opt.label ?? ''}`}>
                                            <X size={18} />
                                        </div>
                                    </Badge>
                                ))}
                                {hiddenCount > 0 && (
                                    <Badge
                                        variant="outline"
                                        className="rounded-md px-1.5 py-0.5 w-fit h-full bg-card flex-shrink-0"
                                        aria-label={`${hiddenCount} more selected`}>
                                        +{hiddenCount} more
                                    </Badge>
                                )}
                            </div>
                        </div>
                    ) : (
                        <div className="grid">
                            <span className="text-base flex-1 flex items-center truncate leading-5 text-input">
                                {title || (
                                    <span className="text-outer-space-400">
                                        {placeholder}
                                    </span>
                                )}
                            </span>
                        </div>
                    )
                ) : (
                    <div className="flex flex-1 items-center gap-2.5">
                        <OptionAvatar
                            profile={(currentValue as Option)?.profile}
                            vessel={(currentValue as Option)?.vessel}
                            label={(currentValue as Option)?.label}
                        />
                        <div className="grid">
                            <span className="text-base truncate leading-5 text-input">
                                {(currentValue as Option)?.label ?? (
                                    <span className="text-outer-space-400">
                                        {placeholder}
                                    </span>
                                )}
                            </span>
                        </div>
                    </div>
                )}
            </div>
        </Button>
    )

    const renderCombobox = () => (
        <>
            <Popover modal={modal} open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    {renderComboboxButton()}
                </PopoverTrigger>
                <PopoverContent
                    align={align}
                    className={cn(
                        'p-0 z-[9999] w-fit sm:min-w-[300px] min-w-[--radix-popover-trigger-width] sm:w-[--radix-popover-trigger-width] max-h-[400px] overflow-y-auto',
                        '[&_cmdk-item][data-selected=true]:bg-transparent [&_cmdk-item][data-selected=true]:text-foreground',
                    )}>
                    {/* Command ------------------------------ */}
                    <Command
                        className="w-full"
                        loop={false}
                        shouldFilter={false}
                        value=""
                        onKeyDown={(e) => {
                            if (e.key === 'ArrowUp' || e.key === 'ArrowDown')
                                setActiveItem('keyboard-nav')
                            else if (e.key === 'Escape') setOpen(false)
                        }}
                        onMouseMove={() =>
                            activeItem === 'keyboard-nav' && setActiveItem(null)
                        }>
                        {/* Search bar */}
                        {options.length >= searchThreshold && (
                            <CommandInput
                                placeholder={searchPlaceholder}
                                className="flex h-9 w-full rounded-md bg-card/0 py-3 text-sm outline-none placeholder:text-outer-space-400 disabled:cursor-not-allowed disabled:opacity-50"
                                value={searchQuery}
                                onValueChange={setSearchQuery}
                            />
                        )}

                        {/* List */}
                        <CommandList className="p-2.5 max-h-[320px] overflow-y-auto">
                            <CommandEmpty>{noResultsMessage}</CommandEmpty>

                            {multi && filteredOptions.length > 0 && (
                                <CommandGroup>
                                    <CommandItem
                                        value="select-all"
                                        onSelect={() => {
                                            handleSelect('select-all')
                                            setActiveItem(null)
                                        }}
                                        data-selected={
                                            activeItem === 'keyboard-nav'
                                                ? undefined
                                                : false
                                        }
                                        className="flex items-center gap-2.5 h-[33px] py-[6px] px-5 hover:bg-background hover:text-primary">
                                        <div
                                            className={cn(
                                                'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                                                filteredOptions
                                                    .filter(
                                                        (opt) =>
                                                            !opt.excludeFromSelectAll,
                                                    )
                                                    .every(
                                                        (opt) =>
                                                            Array.isArray(
                                                                currentValue,
                                                            ) &&
                                                            currentValue.some(
                                                                (c) =>
                                                                    c.value ===
                                                                    opt.value,
                                                            ),
                                                    )
                                                    ? 'bg-primary text-primary-foreground'
                                                    : 'opacity-50 [&_svg]:invisible',
                                            )}>
                                            <Check className="h-3 w-3" />
                                        </div>
                                        <span className="text-base leading-5 text-input">
                                            {selectAllLabel}
                                        </span>
                                    </CommandItem>
                                </CommandGroup>
                            )}

                            {Object.entries(groupedOptions).map(
                                ([grp, opts]) => (
                                    <CommandGroup
                                        key={grp}
                                        heading={
                                            groupBy && grp !== 'ungrouped'
                                                ? grp
                                                : undefined
                                        }>
                                        {opts.map((opt) => (
                                            <CommandItem
                                                key={opt.value}
                                                value={opt.value}
                                                onSelect={() => {
                                                    handleSelect(opt.value)
                                                    setActiveItem(null)
                                                    if (!multi) setOpen(false)
                                                }}
                                                className={cn(
                                                    'flex items-center gap-2.5 h-[33px] py-[6px] px-5 my-1',
                                                    !multi && isSelected(opt)
                                                        ? 'bg-accent text-accent-foreground'
                                                        : '',
                                                    'rounded-md cursor-pointer focus:bg-accent text-input',
                                                    'border border-card/0 hover:bg-accent hover:border hover:border-border',
                                                    opt.className,
                                                )}
                                                data-selected={
                                                    activeItem ===
                                                    'keyboard-nav'
                                                        ? undefined
                                                        : false
                                                }
                                                disabled={isDisabled}>
                                                {multi && (
                                                    <div
                                                        className={cn(
                                                            'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                                                            isSelected(opt)
                                                                ? 'bg-primary text-primary-foreground'
                                                                : 'opacity-50 [&_svg]:invisible',
                                                        )}>
                                                        <Check className="h-3 w-3" />
                                                    </div>
                                                )}
                                                <div className="flex items-center gap-2.5">
                                                    <OptionAvatar
                                                        profile={opt.profile}
                                                        vessel={opt.vessel}
                                                        label={opt.label}
                                                    />
                                                    <span className="text-base leading-5">
                                                        {opt.label ?? ''}
                                                    </span>
                                                    {!multi &&
                                                        isSelected(opt) && (
                                                            <Check className="ml-auto h-4 w-4 text-primary" />
                                                        )}
                                                </div>
                                            </CommandItem>
                                        ))}
                                    </CommandGroup>
                                ),
                            )}
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>

            {selectedOptionsText && (
                <span
                    id={`${comboboxId}-sr`}
                    className="sr-only"
                    aria-live="polite">
                    {selectedOptionsText}
                </span>
            )}
        </>
    )

    /* ----------------------------------------------------------------------- */
    /* Loading state                                                           */
    /* ----------------------------------------------------------------------- */
    if (isLoading) {
        const skeleton = (
            <Skeleton
                className={cn('h-[43px] w-full flex-1', buttonClassName)}
            />
        )

        return label ? (
            <Label
                id={comboboxId}
                label={label}
                position={labelPosition}
                className={labelClassName}
                disabled={isDisabled}>
                {skeleton}
            </Label>
        ) : (
            skeleton
        )
    }

    return label ? (
        <Label
            id={comboboxId}
            label={label}
            position={labelPosition}
            className={cn('w-full', labelClassName)}
            required={required}
            disabled={isDisabled}>
            {renderCombobox()}
        </Label>
    ) : (
        renderCombobox()
    )
}
